# 每日报告数据提取器 - 架构设计文档

## 1. 架构概述

### 1.1 设计原则
- **独立性**：完全独立的模块，不依赖现有业务代码
- **解耦性**：与现有系统零耦合，仅通过配置文件获取必要信息
- **可维护性**：清晰的模块划分和接口设计
- **健壮性**：完整的错误处理和恢复机制
- **可扩展性**：支持未来功能扩展

### 1.2 核心功能
1. 每日7:00自动执行定时任务
2. 扫描指定目录下的当日Excel报告文件
3. 解除Excel工作表保护并提取数据
4. 将数据存储到SQL Server数据库
5. 完整的日志记录和错误处理

## 2. 模块架构设计

### 2.1 核心模块

#### 2.1.1 ConfigurationManager (配置管理器)
```csharp
public class DailyExtractorConfig
{
    public string ReportRootPath { get; set; }
    public string DatabaseConnectionString { get; set; }
    public string ExcelPassword { get; set; }
    public TimeSpan ScheduleTime { get; set; }
    public bool EnableBatchInsert { get; set; }
    public int BatchSize { get; set; }
    public bool EnableDetailedLogging { get; set; }
}

public interface IConfigurationManager
{
    DailyExtractorConfig LoadConfiguration();
    void SaveConfiguration(DailyExtractorConfig config);
}
```

#### 2.1.2 ExcelDataExtractor (Excel数据提取器)
```csharp
public class ExcelReportData
{
    public string DeviceName { get; set; }
    public DateTime ReportDateTime { get; set; }
    public Dictionary<string, decimal> AGA10Results { get; set; }
    public string SourceFilePath { get; set; }
}

public interface IExcelDataExtractor
{
    Task<ExcelReportData> ExtractDataAsync(string filePath);
    Task<List<ExcelReportData>> ExtractBatchDataAsync(IEnumerable<string> filePaths);
}
```

#### 2.1.3 DatabaseManager (数据库操作管理器)
```csharp
public interface IDatabaseManager
{
    Task<bool> InsertReportDataAsync(ExcelReportData data);
    Task<int> InsertBatchReportDataAsync(IEnumerable<ExcelReportData> dataList);
    Task<bool> CheckDuplicateAsync(string deviceName, DateTime reportDateTime);
    Task<bool> TestConnectionAsync();
}
```

#### 2.1.4 ReportFileScanner (报告文件扫描器)
```csharp
public interface IReportFileScanner
{
    Task<IEnumerable<string>> ScanTodayReportsAsync(string rootPath);
    Task<IEnumerable<string>> ScanReportsByDateAsync(string rootPath, DateTime date);
    bool IsValidReportFile(string filePath);
}
```

#### 2.1.5 ScheduledTaskService (定时任务服务)
```csharp
public interface IScheduledTaskService
{
    Task StartAsync();
    Task StopAsync();
    Task ExecuteTaskAsync();
    bool IsRunning { get; }
}
```

### 2.2 主服务类

#### 2.2.1 DailyReportExtractorService (主服务)
```csharp
public class DailyReportExtractorService
{
    private readonly IConfigurationManager _configManager;
    private readonly IExcelDataExtractor _excelExtractor;
    private readonly IDatabaseManager _databaseManager;
    private readonly IReportFileScanner _fileScanner;
    private readonly IScheduledTaskService _scheduler;
    private readonly ILogger _logger;

    public async Task StartServiceAsync();
    public async Task StopServiceAsync();
    public async Task<ProcessingResult> ProcessTodayReportsAsync();
}
```

## 3. 数据流设计

### 3.1 处理流程
```
定时触发(7:00) → 扫描文件 → 提取数据 → 验证数据 → 存储数据 → 记录日志
```

### 3.2 错误处理流程
```
异常捕获 → 错误分类 → 日志记录 → 恢复策略 → 继续处理/停止服务
```

## 4. 配置文件设计

### 4.1 DailyExtractorConfig.xml
```xml
<?xml version="1.0" encoding="utf-8"?>
<DailyExtractorConfiguration>
  <Schedule>
    <ExecutionTime>07:00:00</ExecutionTime>
    <Enabled>true</Enabled>
  </Schedule>
  
  <ReportSettings>
    <RootPath>D:\Report\</RootPath>
    <ExcelPassword>Admin123</ExcelPassword>
    <FileNamePattern>GUSM_AutoSOSCHECK_*.xlsx</FileNamePattern>
  </ReportSettings>
  
  <DatabaseSettings>
    <ConnectionString>Data Source=192.168.20.86;Initial Catalog=DaPeng_IOServer;User Id=sa;Password=********;</ConnectionString>
    <TableName>SOSCheckResult</TableName>
    <EnableBatchInsert>true</EnableBatchInsert>
    <BatchSize>100</BatchSize>
  </DatabaseSettings>
  
  <LoggingSettings>
    <EnableDetailedLogging>true</EnableDetailedLogging>
    <LogLevel>Info</LogLevel>
    <LogFilePath>Logs\DailyExtractor.log</LogFilePath>
  </LoggingSettings>
</DailyExtractorConfiguration>
```

## 5. 部署结构

### 5.1 目录结构
```
FlowCheck-DPLNG/
├── DailyReportExtractor/
│   ├── Services/
│   │   ├── DailyReportExtractorService.cs
│   │   ├── ScheduledTaskService.cs
│   │   └── ConfigurationManager.cs
│   ├── DataAccess/
│   │   ├── DatabaseManager.cs
│   │   └── Models/
│   ├── ExcelProcessing/
│   │   ├── ExcelDataExtractor.cs
│   │   └── ReportFileScanner.cs
│   ├── Config/
│   │   └── DailyExtractorConfig.xml
│   └── Logs/
├── Scripts/
│   └── CreateSOSCheckResultTable.sql
└── Docs/
    └── DailyExtractor_DeploymentGuide.md
```

## 6. 技术栈

### 6.1 核心依赖
- **.NET Framework 4.8** (与现有项目保持一致)
- **EPPlus** (Excel文件处理)
- **System.Data.SqlClient** (SQL Server连接)
- **NLog** (日志记录)
- **System.Threading.Timer** (定时任务)

### 6.2 设计模式
- **依赖注入** (Dependency Injection)
- **工厂模式** (Factory Pattern)
- **策略模式** (Strategy Pattern)
- **观察者模式** (Observer Pattern)

## 7. 性能和安全考虑

### 7.1 性能优化
- 批量数据库操作
- 异步文件处理
- 内存优化的Excel读取
- 连接池管理

### 7.2 安全措施
- 配置文件加密
- 数据库连接安全
- 异常信息脱敏
- 访问权限控制

## 8. 监控和维护

### 8.1 监控指标
- 任务执行状态
- 处理文件数量
- 数据库操作性能
- 错误率统计

### 8.2 维护功能
- 日志轮转
- 配置热更新
- 服务状态检查
- 手动任务触发
