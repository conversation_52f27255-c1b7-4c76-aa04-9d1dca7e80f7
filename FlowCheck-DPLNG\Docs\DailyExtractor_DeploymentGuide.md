# 每日报告数据提取器 - 部署指南

## 1. 概述

每日报告数据提取器是一个独立的后台服务模块，用于自动提取Excel报告中的SOS检查数据并存储到SQL Server数据库中。

### 1.1 主要功能
- 每日7:00自动执行（可配置）
- 扫描指定目录下的Excel报告文件
- 解除Excel工作表保护并提取数据
- 将数据存储到SOSCheckResult表
- 完整的错误处理和日志记录
- 防重复数据插入

## 2. 系统要求

### 2.1 软件要求
- .NET Framework 4.8 或更高版本
- Microsoft Office Excel 2016 或更高版本（或兼容的Excel处理组件）
- SQL Server 2012 或更高版本

### 2.2 硬件要求
- 内存：至少512MB可用内存
- 磁盘空间：至少100MB可用空间（用于日志和临时文件）
- 网络：能够访问SQL Server数据库

## 3. 安装步骤

### 3.1 数据库准备

1. **创建数据表**
   ```sql
   -- 在SQL Server中执行以下脚本
   -- 脚本位置: FlowCheck-DPLNG\Scripts\CreateSOSCheckResultTable.sql
   ```

2. **验证表创建**
   ```sql
   SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
   WHERE TABLE_NAME = 'SOSCheckResult'
   ```

### 3.2 文件部署

1. **复制模块文件**
   - 将整个 `DailyReportExtractor` 文件夹复制到主程序目录下
   - 确保目录结构如下：
   ```
   FlowCheck-DPLNG/
   ├── DailyReportExtractor/
   │   ├── Services/
   │   ├── DataAccess/
   │   ├── ExcelProcessing/
   │   ├── Models/
   │   └── Config/
   ```

2. **配置文件设置**
   - 编辑 `DailyReportExtractor/Config/DailyExtractorConfig.xml`
   - 根据实际环境修改配置参数

### 3.3 配置参数说明

#### 3.3.1 调度设置
```xml
<Schedule>
  <ExecutionTime>07:00:00</ExecutionTime>  <!-- 执行时间 -->
  <Enabled>true</Enabled>                  <!-- 是否启用 -->
</Schedule>
```

#### 3.3.2 报告文件设置
```xml
<ReportSettings>
  <RootPath>D:\Report\</RootPath>          <!-- 报告根目录 -->
  <ExcelPassword>Admin123</ExcelPassword>  <!-- Excel****** -->
  <FileNamePattern>GUSM_AutoSOSCHECK</FileNamePattern> <!-- 文件名模式 -->
</ReportSettings>
```

#### 3.3.3 数据库设置
```xml
<DatabaseSettings>
  <ConnectionString>Data Source=服务器地址;Initial Catalog=数据库名;User Id=用户名;Password=******;</ConnectionString>
  <TableName>SOSCheckResult</TableName>
  <EnableBatchInsert>true</EnableBatchInsert>
  <BatchSize>100</BatchSize>
</DatabaseSettings>
```

## 4. 集成到主程序

### 4.1 修改主程序代码

在 `Form1.cs` 的构造函数中添加以下代码：

```csharp
// 添加字段
private DailyReportExtractorService _dailyExtractorService;

// 在构造函数中添加
try
{
    _dailyExtractorService = new DailyReportExtractorService();
    await _dailyExtractorService.StartServiceAsync();
    Log.Info("DailyReportExtractorService started successfully");
}
catch (Exception ex)
{
    Log.Error($"Failed to start DailyReportExtractorService: {ex.Message}", ex);
    // 服务启动失败不影响主程序功能
}
```

### 4.2 添加清理代码

在程序退出时添加清理代码：

```csharp
// 在Form1的FormClosing事件或Dispose方法中添加
try
{
    _dailyExtractorService?.StopServiceAsync().Wait(5000);
    _dailyExtractorService?.Dispose();
}
catch (Exception ex)
{
    Log.Error($"Error stopping DailyReportExtractorService: {ex.Message}", ex);
}
```

## 5. 测试验证

### 5.1 功能测试

1. **配置验证**
   ```csharp
   var service = new DailyReportExtractorService();
   var stats = await service.GetProcessingStatisticsAsync();
   Console.WriteLine($"Service Status: {stats}");
   ```

2. **手动执行测试**
   ```csharp
   var result = await service.ExecuteManuallyAsync();
   Console.WriteLine($"Processing Result: {result.GetSummary()}");
   ```

3. **数据库验证**
   ```sql
   SELECT TOP 10 * FROM SOSCheckResult 
   ORDER BY RecordCreatedAt DESC
   ```

### 5.2 日志检查

检查日志文件：`Logs\DailyExtractor.log`

正常启动日志示例：
```
2024-01-15 08:00:00 INFO  - 初始化每日报告提取器服务
2024-01-15 08:00:01 INFO  - 配置加载成功 - 执行时间: 07:00:00, 启用: True
2024-01-15 08:00:02 INFO  - 每日报告提取器服务初始化完成
2024-01-15 08:00:03 INFO  - 启动每日报告提取器服务
2024-01-15 08:00:04 INFO  - 数据库连接测试成功
2024-01-15 08:00:05 INFO  - 下次执行时间: 2024-01-16 07:00:00
```

## 6. 运维管理

### 6.1 监控要点

1. **服务状态监控**
   - 检查服务是否正常运行
   - 监控下次执行时间
   - 查看最后执行时间

2. **数据处理监控**
   - 每日处理的文件数量
   - 成功插入的记录数
   - 跳过的重复记录数

3. **错误监控**
   - 检查日志中的ERROR和WARN级别消息
   - 监控数据库连接状态
   - 文件访问权限问题

### 6.2 常见问题处理

#### 6.2.1 服务无法启动
- 检查配置文件是否存在且格式正确
- 验证数据库连接字符串
- 确认报告目录路径存在且有访问权限

#### 6.2.2 文件扫描失败
- 检查报告根目录路径配置
- 验证目录访问权限
- 确认文件名模式匹配

#### 6.2.3 Excel数据提取失败
- 验证Excel文件格式
- 检查工作表名称是否正确
- 确认Excel******设置

#### 6.2.4 数据库插入失败
- 检查数据库连接
- 验证表结构是否正确
- 确认数据类型匹配

### 6.3 性能优化

1. **批量处理优化**
   - 启用批量插入：`<EnableBatchInsert>true</EnableBatchInsert>`
   - 调整批量大小：`<BatchSize>100</BatchSize>`

2. **内存优化**
   - 处理大量文件时，考虑分批处理
   - 及时释放Excel文件资源

3. **日志优化**
   - 生产环境可设置日志级别为Info或Warn
   - 定期清理历史日志文件

## 7. 故障排除

### 7.1 诊断工具

1. **配置验证工具**
   ```csharp
   var configManager = new ConfigurationManager();
   var config = configManager.LoadConfiguration();
   var validation = config.Validate();
   if (!validation.IsValid)
   {
       Console.WriteLine($"配置错误: {string.Join(", ", validation.Errors)}");
   }
   ```

2. **数据库连接测试**
   ```csharp
   var dbManager = new DatabaseManager(connectionString);
   var connected = await dbManager.TestConnectionAsync();
   Console.WriteLine($"数据库连接: {(connected ? "成功" : "失败")}");
   ```

### 7.2 应急处理

1. **紧急停止服务**
   - 修改配置文件：`<Enabled>false</Enabled>`
   - 重启主程序

2. **手动数据处理**
   - 使用手动执行功能处理遗漏的数据
   - 检查并修复数据完整性

## 8. 更新升级

### 8.1 版本更新步骤

1. 停止服务
2. 备份配置文件
3. 替换程序文件
4. 恢复配置文件
5. 重启服务
6. 验证功能

### 8.2 配置迁移

升级时注意保留用户自定义的配置参数，特别是：
- 数据库连接字符串
- 报告目录路径
- 执行时间设置
- Excel******

## 9. 联系支持

如遇到技术问题，请提供以下信息：
- 错误日志文件
- 配置文件内容
- 系统环境信息
- 问题复现步骤
