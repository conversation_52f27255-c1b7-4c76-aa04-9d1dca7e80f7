### **新功能开发需求：每日自动抽取Excel报告数据并存入SQL Server数据库**

#### **1\. 需求概述**

在现有项目中，开发一个全新的、**独立的后台定时任务模块**。该模块的核心功能是：在每日指定时间，自动查找并解析由系统中“自动报告程序”生成的当日所有 Excel 报告文件，从中提取指定的关键业务数据，最后将这些数据整合并持久化存储到 SQL Server 数据库的一个专用表中。

**此功能的实现必须确保与项目中现有功能完全解耦，不得影响任何已有的业务逻辑和代码。**

#### **1.5. 架构要求**

* **模块独立性**：此新功能必须作为一个独立的、自包含的模块或服务进行开发。  
* **低耦合**：应最大限度地减少与项目现有代码的依赖关系。除了必要的配置信息（如数据库连接字符串、报告目录路径）的读取外，不应与现有业务模块产生直接的代码耦合。  
* **无侵入性**：开发过程不应对现有任何功能（包括“自动报告程序”）的代码进行任何修改。新增的功能模块应该是纯粹的“添加”，而不是“修改”。这保证了新功能的开发、部署、维护和启停都不会对现有系统的稳定性造成任何风险。

#### **2\. 核心功能逻辑**

1. **定时触发**  
   * 任务调度：配置此任务于**每天早上 7:00** 准时自动执行。  
2. **报告文件定位与读取**  
   * **路径与规则**：首先，请分析现有项目中“自动报告程序”的相关代码，以确定其生成报告的**存储目录**和**文件命名规则**（例如：设备名\_日期\_报告类型.xlsx）。  
   * **文件筛选**：任务启动后，扫描指定目录，精确找出所有在**任务执行当天**生成的报告文件。  
   * **处理工作表保护**：请注意，目标 Excel 文件中的工作表（特别是 Inspection Report 和 AGA10 Result）均设置了密码保护。在读取单元格数据之前，程序必须使用密码 **Admin123** 来解除工作表的保护。  
   * **数据遍历**：遍历当天找到的所有报告文件，并对每一个文件执行后续的数据抽取操作。  
3. **数据抽取**  
   * 对于每一个 Excel 文件，需要从两个不同的工作表 (Sheet) 中获取数据：  
   * 从 **“Inspection Report”** 工作表：  
     * **设备名称 (DeviceName)**：获取单元格 B2 的值。  
     * **报告日期 (ReportDate)**：获取单元格 K1 的值。  
     * **报告时间 (ReportTime)**：获取单元格 K2 的值。  
   * 从 **“AGA10 Result”** 工作表：  
     * 获取下文“数据库字段映射”部分所列出的所有业务参数值。  
4. **数据存储**  
   * 将从**单个 Excel 文件**中提取出的所有字段（设备名称、报告时间及所有业务参数）组合成一条完整的记录。  
   * 将这条记录作为新的一行插入到 SQL Server 的 SOSCheckResult 表中。

#### **3\. 数据库设计**

* 请为本功能设计 SOSCheckResult 数据表，并提供完整的 **T-SQL CREATE TABLE 脚本**。  
* 建议表中包含一个自增整数作为主键（Primary Key），例如 ID。  
* **字段映射关系**如下表所示，请为每个字段选择最合适的 SQL Server 数据类型（例如 NVARCHAR(255), DECIMAL(18, 4), DATETIME2(3) 等）。

| 数据库字段 (Column Name) | 数据来源 (Source) | Excel工作表 (Sheet) | 单元格 (Cell) | 建议数据类型 | 备注 |
| :---- | :---- | :---- | :---- | :---- | :---- |
| ID | 自增主键 | \- | \- | INT | Primary Key, IDENTITY(1,1) |
| DeviceName | 设备名 | Inspection Report | B2 | NVARCHAR(255) |  |
| ReportDateTime | 报告生成时间 | Inspection Report | K1 \+ K2 | DATETIME2(3) | 合并日期和时间 |
| Press | Press | AGA10 Result | B2 | DECIMAL(18, 4\) |  |
| Temp | Temp | AGA10 Result | B3 | DECIMAL(18, 4\) |  |
| C1 | C1 | AGA10 Result | B5 | DECIMAL(18, 4\) |  |
| C2 | C2 | AGA10 Result | B6 | DECIMAL(18, 4\) |  |
| C3 | C3 | AGA10 Result | B7 | DECIMAL(18, 4\) |  |
| nC4 | nC4 | AGA10 Result | B8 | DECIMAL(18, 4\) |  |
| iC4 | iC4 | AGA10 Result | B9 | DECIMAL(18, 4\) |  |
| nC5 | nC5 | AGA10 Result | B10 | DECIMAL(18, 4\) |  |
| iC5 | iC5 | AGA10 Result | B11 | DECIMAL(18, 4\) |  |
| neoC5 | neoC5 | AGA10 Result | B12 | DECIMAL(18, 4\) |  |
| C6 | C6 | AGA10 Result | B13 | DECIMAL(18, 4\) |  |
| N2 | N2 | AGA10 Result | B14 | DECIMAL(18, 4\) |  |
| CO2 | CO2 | AGA10 Result | B15 | DECIMAL(18, 4\) |  |
| Total | Total | AGA10 Result | B16 | DECIMAL(18, 4\) |  |
| AvgSOS | AvgSOS | AGA10 Result | B18 | DECIMAL(18, 4\) |  |
| CalculateSOS | CalculateSOS | AGA10 Result | B19 | DECIMAL(18, 4\) |  |
| Dev | Dev | AGA10 Result | B20 | DECIMAL(18, 4\) |  |
| RecordCreatedAt | 记录创建时间 | 系统生成 | \- | DATETIME2(3) | DEFAULT GETDATE() |

#### **4\. 非功能性需求 (健壮性与性能)**

1. **健壮性与错误处理**  
   * **全面的异常捕获**：必须对所有潜在的失败场景进行处理，包括但不限于：  
     * 报告目录不存在或无法访问。  
     * 当天没有任何报告文件。  
     * Excel 文件已损坏、被加密或格式不兼容。  
     * **解除工作表保护失败（例如，密码不正确）**。  
     * 指定的 Inspection Report 或 AGA10 Result 工作表不存在。  
     * 指定单元格为空或数据格式不正确（例如，数字字段中包含文本）。  
     * 数据库连接失败或写入超时。  
   * **详细的日志记录**：在任务的**关键节点**（如开始、结束、找到的文件总数、成功/失败处理的文件名）以及**所有异常发生时**，都必须记录清晰、详细的日志。  
   * **任务幂等性**：需要考虑任务在同一天被意外重复执行的场景。通过在插入数据前，基于**设备名**和**报告时间**进行唯一性检查，防止同一份报告的数据被重复录入数据库。  
2. **性能要求**  
   * **高效I/O**：在处理大量报告文件时，应采用高效的文件读取和数据处理策略，避免长时间占用CPU和内存资源。  
   * **数据库批量操作**：如果单次任务需要处理的报告数量较多（例如超过100个），应优先考虑使用**批量插入 (Bulk Insert)** 的方式将数据写入数据库，以获得远高于逐条插入的性能。

#### **5\. 交付产物清单**

1. **源代码**：实现上述所有功能的、注释清晰、结构良好的完整代码。  
2. **SQL脚本**：用于创建 SOSCheckResult 表的 T-SQL 脚本文件。  
3. **配置与部署说明**：提供简要的文档，说明如何配置必要的参数（如报告文件目录、数据库连接字符串等）以及如何部署和启动这个定时任务。