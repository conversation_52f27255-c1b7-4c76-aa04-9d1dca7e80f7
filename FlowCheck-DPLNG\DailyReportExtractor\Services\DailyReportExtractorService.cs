using System;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using FlowCheck_DPLNG.DailyReportExtractor.DataAccess;
using FlowCheck_DPLNG.DailyReportExtractor.ExcelProcessing;
using FlowCheck_DPLNG.DailyReportExtractor.Models;
using log4net;

namespace FlowCheck_DPLNG.DailyReportExtractor.Services
{
    /// <summary>
    /// 每日报告提取器主服务
    /// 集成所有功能模块，提供完整的报告数据提取服务
    /// </summary>
    public class DailyReportExtractorService : IDisposable
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(DailyReportExtractorService));

        // 核心组件
        private readonly IConfigurationManager _configManager;
        private readonly IExcelDataExtractor _excelExtractor;
        private readonly IDatabaseManager _databaseManager;
        private readonly IReportFileScanner _fileScanner;
        private readonly IScheduledTaskService _scheduler;

        // 配置和状态
        private DailyExtractorConfig _config;
        private bool _isInitialized;
        private bool _isDisposed;

        /// <summary>
        /// 服务是否正在运行
        /// </summary>
        public bool IsRunning => _scheduler?.IsRunning ?? false;

        /// <summary>
        /// 服务状态
        /// </summary>
        public ServiceStatus Status => _scheduler?.GetStatus();

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="configFilePath">配置文件路径（可选）</param>
        public DailyReportExtractorService(string configFilePath = null)
        {
            try
            {
                _logger.Info("初始化每日报告提取器服务");

                // 初始化配置管理器
                _configManager = new ConfigurationManager(configFilePath);
                
                // 加载配置
                _config = _configManager.LoadConfiguration();
                _logger.Info($"配置加载成功 - 执行时间: {_config.ScheduleTime:hh\\:mm\\:ss}, 启用: {_config.Enabled}");

                // 初始化各个组件
                _excelExtractor = new ExcelDataExtractor(_config.ExcelPassword);
                _databaseManager = new DatabaseManager(_config.DatabaseConnectionString, _config.DatabaseTableName);
                _fileScanner = new ReportFileScanner(_config.FileNamePattern);
                _scheduler = new ScheduledTaskService(_config.ScheduleTime);

                // 设置定时任务
                _scheduler.TaskToExecute = ProcessTodayReportsAsync;

                _isInitialized = true;
                _logger.Info("每日报告提取器服务初始化完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"初始化每日报告提取器服务失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 启动服务
        /// </summary>
        public async Task StartServiceAsync()
        {
            if (_isDisposed)
                throw new ObjectDisposedException(nameof(DailyReportExtractorService));

            if (!_isInitialized)
                throw new InvalidOperationException("服务未初始化");

            if (!_config.Enabled)
            {
                _logger.Warn("服务已禁用，跳过启动");
                return;
            }

            try
            {
                _logger.Info("启动每日报告提取器服务");

                // 测试数据库连接
                var dbConnected = await _databaseManager.TestConnectionAsync();
                if (!dbConnected)
                {
                    throw new InvalidOperationException("数据库连接测试失败");
                }

                // 启动定时任务
                await _scheduler.StartAsync();

                _logger.Info("每日报告提取器服务启动成功");
            }
            catch (Exception ex)
            {
                _logger.Error($"启动服务失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 停止服务
        /// </summary>
        public async Task StopServiceAsync()
        {
            if (_isDisposed)
                return;

            try
            {
                _logger.Info("停止每日报告提取器服务");

                if (_scheduler != null)
                {
                    await _scheduler.StopAsync();
                }

                _logger.Info("每日报告提取器服务停止成功");
            }
            catch (Exception ex)
            {
                _logger.Error($"停止服务失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 手动执行今日报告处理
        /// </summary>
        public async Task<ProcessingResult> ExecuteManuallyAsync()
        {
            if (_isDisposed)
                throw new ObjectDisposedException(nameof(DailyReportExtractorService));

            _logger.Info("手动执行今日报告处理");
            return await ProcessTodayReportsAsync();
        }

        /// <summary>
        /// 处理今日报告（核心业务逻辑）
        /// </summary>
        public async Task<ProcessingResult> ProcessTodayReportsAsync()
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new ProcessingResult();

            try
            {
                _logger.Info("开始处理今日报告");

                // 1. 扫描今日报告文件
                var reportFiles = await _fileScanner.ScanTodayReportsAsync(_config.ReportRootPath);
                var fileList = reportFiles.ToList();
                
                result.TotalFiles = fileList.Count;
                _logger.Info($"找到 {result.TotalFiles} 个今日报告文件");

                if (result.TotalFiles == 0)
                {
                    _logger.Info("未找到今日报告文件，处理完成");
                    result.Success = true;
                    return result;
                }

                // 2. 提取Excel数据
                _logger.Info("开始提取Excel数据");
                var extractedDataList = await _excelExtractor.ExtractBatchDataAsync(fileList);
                
                result.ProcessedFiles = extractedDataList.Count;
                _logger.Info($"成功提取 {result.ProcessedFiles} 个文件的数据");

                if (extractedDataList.Count == 0)
                {
                    result.Warnings.Add("所有文件数据提取失败");
                    _logger.Warn("所有文件数据提取失败");
                    return result;
                }

                // 3. 存储数据到数据库
                _logger.Info("开始存储数据到数据库");
                
                if (_config.EnableBatchInsert && extractedDataList.Count > 1)
                {
                    // 批量插入
                    result.SuccessfulInserts = await _databaseManager.InsertBatchReportDataAsync(extractedDataList);
                }
                else
                {
                    // 逐条插入
                    foreach (var data in extractedDataList)
                    {
                        var inserted = await _databaseManager.InsertReportDataAsync(data);
                        if (inserted)
                        {
                            result.SuccessfulInserts++;
                        }
                        else
                        {
                            result.SkippedDuplicates++;
                        }
                    }
                }

                // 4. 统计结果
                result.SkippedDuplicates = result.ProcessedFiles - result.SuccessfulInserts;
                result.Success = result.SuccessfulInserts > 0 || result.SkippedDuplicates > 0;

                stopwatch.Stop();
                result.ProcessingTime = stopwatch.Elapsed;

                _logger.Info($"今日报告处理完成 - {result.GetSummary()}");

                // 5. 记录统计信息
                await LogProcessingStatisticsAsync(result);

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                result.ProcessingTime = stopwatch.Elapsed;
                result.Success = false;
                result.Errors.Add($"处理异常: {ex.Message}");
                
                _logger.Error($"处理今日报告异常: {ex.Message}", ex);
                return result;
            }
        }

        /// <summary>
        /// 获取处理统计信息
        /// </summary>
        public async Task<ProcessingStatistics> GetProcessingStatisticsAsync()
        {
            try
            {
                var stats = new ProcessingStatistics();

                // 获取今日记录数量
                stats.TodayRecordCount = await _databaseManager.GetTodayRecordCountAsync();

                // 获取文件扫描统计
                var scanStats = await _fileScanner.GetScanStatisticsAsync(_config.ReportRootPath);
                stats.TotalReportFiles = scanStats.TotalFiles;
                stats.TodayReportFiles = scanStats.TodayFiles;

                // 获取服务状态
                stats.ServiceStatus = _scheduler?.GetStatus();

                return stats;
            }
            catch (Exception ex)
            {
                _logger.Error($"获取处理统计信息异常: {ex.Message}", ex);
                return new ProcessingStatistics();
            }
        }

        /// <summary>
        /// 重新加载配置
        /// </summary>
        public async Task ReloadConfigurationAsync()
        {
            try
            {
                _logger.Info("重新加载配置");

                var newConfig = _configManager.LoadConfiguration();
                var needRestart = !_config.ScheduleTime.Equals(newConfig.ScheduleTime) || 
                                 _config.Enabled != newConfig.Enabled;

                _config = newConfig;

                if (needRestart && IsRunning)
                {
                    _logger.Info("配置变更需要重启服务");
                    await StopServiceAsync();
                    await StartServiceAsync();
                }

                _logger.Info("配置重新加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"重新加载配置失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 记录处理统计信息
        /// </summary>
        private async Task LogProcessingStatisticsAsync(ProcessingResult result)
        {
            try
            {
                var todayCount = await _databaseManager.GetTodayRecordCountAsync();
                _logger.Info($"今日数据库记录总数: {todayCount}");

                if (result.Errors.Any())
                {
                    _logger.Warn($"处理错误: {string.Join("; ", result.Errors)}");
                }

                if (result.Warnings.Any())
                {
                    _logger.Warn($"处理警告: {string.Join("; ", result.Warnings)}");
                }
            }
            catch (Exception ex)
            {
                _logger.Debug($"记录统计信息异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed)
                return;

            try
            {
                _logger.Info("释放每日报告提取器服务资源");

                StopServiceAsync().Wait(5000);

                _scheduler?.Dispose();

                _isDisposed = true;
                _logger.Info("资源释放完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"释放资源异常: {ex.Message}", ex);
            }
        }
    }

    /// <summary>
    /// 处理统计信息
    /// </summary>
    public class ProcessingStatistics
    {
        public int TodayRecordCount { get; set; }
        public int TotalReportFiles { get; set; }
        public int TodayReportFiles { get; set; }
        public ServiceStatus ServiceStatus { get; set; }
        public DateTime StatisticsTime { get; set; } = DateTime.Now;

        public override string ToString()
        {
            return $"Today Records: {TodayRecordCount}, Today Files: {TodayReportFiles}, " +
                   $"Total Files: {TotalReportFiles}, Service: {ServiceStatus?.IsRunning ?? false}";
        }
    }
}
