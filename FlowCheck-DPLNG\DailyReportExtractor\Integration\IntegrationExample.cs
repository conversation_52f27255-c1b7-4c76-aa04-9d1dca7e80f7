using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using FlowCheck_DPLNG.DailyReportExtractor.Services;
using log4net;

namespace FlowCheck_DPLNG.DailyReportExtractor.Integration
{
    /// <summary>
    /// 集成示例类
    /// 展示如何在主程序中集成每日报告提取器服务
    /// </summary>
    public class IntegrationExample
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(IntegrationExample));
        private DailyReportExtractorService _extractorService;

        /// <summary>
        /// 在Form1构造函数中调用此方法来初始化服务
        /// </summary>
        public async Task InitializeExtractorServiceAsync()
        {
            try
            {
                _logger.Info("初始化每日报告提取器服务");

                // 创建服务实例
                _extractorService = new DailyReportExtractorService();

                // 启动服务
                await _extractorService.StartServiceAsync();

                _logger.Info("每日报告提取器服务启动成功");
            }
            catch (Exception ex)
            {
                _logger.Error($"初始化每日报告提取器服务失败: {ex.Message}", ex);
                
                // 服务启动失败不应影响主程序运行
                MessageBox.Show(
                    $"每日报告提取器服务启动失败：{ex.Message}\n\n主程序将继续运行，但自动报告提取功能不可用。",
                    "服务启动警告",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 在程序退出时调用此方法来清理资源
        /// </summary>
        public async Task CleanupExtractorServiceAsync()
        {
            try
            {
                if (_extractorService != null)
                {
                    _logger.Info("停止每日报告提取器服务");
                    
                    await _extractorService.StopServiceAsync();
                    _extractorService.Dispose();
                    _extractorService = null;

                    _logger.Info("每日报告提取器服务已停止");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"停止每日报告提取器服务异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 手动执行报告提取（可以添加到菜单或按钮事件中）
        /// </summary>
        public async Task ExecuteManualExtractionAsync()
        {
            try
            {
                if (_extractorService == null)
                {
                    MessageBox.Show("每日报告提取器服务未初始化", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                _logger.Info("手动执行报告提取");

                // 显示进度提示
                var progressForm = new Form
                {
                    Text = "处理中...",
                    Size = new System.Drawing.Size(300, 100),
                    StartPosition = FormStartPosition.CenterParent,
                    FormBorderStyle = FormBorderStyle.FixedDialog,
                    MaximizeBox = false,
                    MinimizeBox = false
                };

                var label = new Label
                {
                    Text = "正在处理今日报告，请稍候...",
                    Dock = DockStyle.Fill,
                    TextAlign = System.Drawing.ContentAlignment.MiddleCenter
                };
                progressForm.Controls.Add(label);

                progressForm.Show();

                try
                {
                    // 执行提取
                    var result = await _extractorService.ExecuteManuallyAsync();

                    progressForm.Close();

                    // 显示结果
                    var message = $"处理完成！\n\n" +
                                 $"总文件数: {result.TotalFiles}\n" +
                                 $"处理文件数: {result.ProcessedFiles}\n" +
                                 $"成功插入: {result.SuccessfulInserts}\n" +
                                 $"跳过重复: {result.SkippedDuplicates}\n" +
                                 $"处理时间: {result.ProcessingTime.TotalSeconds:F2} 秒";

                    if (result.Errors.Count > 0)
                    {
                        message += $"\n\n错误: {string.Join("; ", result.Errors)}";
                    }

                    MessageBox.Show(message, "处理结果", MessageBoxButtons.OK, 
                        result.Success ? MessageBoxIcon.Information : MessageBoxIcon.Warning);
                }
                finally
                {
                    if (progressForm.Visible)
                        progressForm.Close();
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"手动执行报告提取异常: {ex.Message}", ex);
                MessageBox.Show($"执行失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 获取服务状态信息（可以用于状态栏显示）
        /// </summary>
        public async Task<string> GetServiceStatusAsync()
        {
            try
            {
                if (_extractorService == null)
                    return "服务未初始化";

                var stats = await _extractorService.GetProcessingStatisticsAsync();
                var status = _extractorService.Status;

                return $"提取器服务: {(status?.IsRunning == true ? "运行中" : "已停止")} | " +
                       $"今日记录: {stats.TodayRecordCount} | " +
                       $"下次执行: {status?.NextExecutionTime?.ToString("MM-dd HH:mm") ?? "未知"}";
            }
            catch (Exception ex)
            {
                _logger.Debug($"获取服务状态异常: {ex.Message}");
                return "状态获取失败";
            }
        }

        /// <summary>
        /// 检查服务健康状态
        /// </summary>
        public bool IsServiceHealthy()
        {
            try
            {
                return _extractorService?.IsRunning == true;
            }
            catch
            {
                return false;
            }
        }
    }

    /// <summary>
    /// Form1集成示例
    /// 展示如何在主窗体中集成服务
    /// </summary>
    public partial class Form1IntegrationExample : Form
    {
        private IntegrationExample _integrationHelper;

        // 在Form1构造函数中添加以下代码
        private async void InitializeServices()
        {
            try
            {
                _integrationHelper = new IntegrationExample();
                await _integrationHelper.InitializeExtractorServiceAsync();

                // 可选：添加状态栏显示
                UpdateStatusBar();

                // 可选：设置定时器定期更新状态
                var statusTimer = new Timer { Interval = 60000 }; // 每分钟更新一次
                statusTimer.Tick += async (s, e) => await UpdateStatusBarAsync();
                statusTimer.Start();
            }
            catch (Exception ex)
            {
                Log.Error($"初始化服务失败: {ex.Message}", ex);
            }
        }

        // 在FormClosing事件中添加以下代码
        private async void Form1_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                if (_integrationHelper != null)
                {
                    await _integrationHelper.CleanupExtractorServiceAsync();
                }
            }
            catch (Exception ex)
            {
                Log.Error($"清理服务异常: {ex.Message}", ex);
            }
        }

        // 可选：添加菜单项或按钮来手动执行
        private async void btnManualExtract_Click(object sender, EventArgs e)
        {
            if (_integrationHelper != null)
            {
                await _integrationHelper.ExecuteManualExtractionAsync();
                await UpdateStatusBarAsync();
            }
        }

        // 更新状态栏
        private async Task UpdateStatusBarAsync()
        {
            try
            {
                if (_integrationHelper != null)
                {
                    var status = await _integrationHelper.GetServiceStatusAsync();
                    // 假设有一个状态栏标签
                    // statusLabel.Text = status;
                }
            }
            catch (Exception ex)
            {
                Log.Debug($"更新状态栏异常: {ex.Message}");
            }
        }

        private void UpdateStatusBar()
        {
            Task.Run(async () => await UpdateStatusBarAsync());
        }
    }
}
