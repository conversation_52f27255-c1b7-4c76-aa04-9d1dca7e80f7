using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using FlowCheck_DPLNG.DailyReportExtractor.Models;
using log4net;

namespace FlowCheck_DPLNG.DailyReportExtractor.DataAccess
{
    /// <summary>
    /// 数据库操作管理器接口
    /// </summary>
    public interface IDatabaseManager
    {
        Task<bool> InsertReportDataAsync(ExcelReportData data);
        Task<int> InsertBatchReportDataAsync(IEnumerable<ExcelReportData> dataList);
        Task<bool> CheckDuplicateAsync(string deviceName, DateTime reportDateTime);
        Task<bool> TestConnectionAsync();
        Task<int> GetTodayRecordCountAsync();
    }

    /// <summary>
    /// 数据库操作管理器实现
    /// 负责SOSCheckResult表的数据操作
    /// </summary>
    public class DatabaseManager : IDatabaseManager
    {
        private readonly string _connectionString;
        private readonly string _tableName;
        private static readonly ILog _logger = LogManager.GetLogger(typeof(DatabaseManager));

        // SQL语句常量
        private const string INSERT_SQL = @"
            INSERT INTO [dbo].[SOSCheckResult] 
            ([DeviceName], [ReportDateTime], [Press], [Temp], [C1], [C2], [C3], [nC4], [iC4], 
             [nC5], [iC5], [neoC5], [C6], [N2], [CO2], [Total], [AvgSOS], [CalculateSOS], [Dev], 
             [SourceFilePath], [ProcessedBy])
            VALUES 
            (@DeviceName, @ReportDateTime, @Press, @Temp, @C1, @C2, @C3, @nC4, @iC4, 
             @nC5, @iC5, @neoC5, @C6, @N2, @CO2, @Total, @AvgSOS, @CalculateSOS, @Dev, 
             @SourceFilePath, @ProcessedBy)";

        private const string CHECK_DUPLICATE_SQL = @"
            SELECT COUNT(1) FROM [dbo].[SOSCheckResult] 
            WHERE [DeviceName] = @DeviceName AND [ReportDateTime] = @ReportDateTime";

        private const string COUNT_TODAY_SQL = @"
            SELECT COUNT(1) FROM [dbo].[SOSCheckResult] 
            WHERE CAST([RecordCreatedAt] AS DATE) = CAST(GETDATE() AS DATE)";

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <param name="tableName">表名（默认为SOSCheckResult）</param>
        public DatabaseManager(string connectionString, string tableName = "SOSCheckResult")
        {
            _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
            _tableName = tableName ?? "SOSCheckResult";
        }

        /// <summary>
        /// 插入单条报告数据
        /// </summary>
        /// <param name="data">报告数据</param>
        /// <returns>是否成功</returns>
        public async Task<bool> InsertReportDataAsync(ExcelReportData data)
        {
            if (data == null)
                throw new ArgumentNullException(nameof(data));

            try
            {
                // 验证数据
                var validation = data.Validate();
                if (!validation.IsValid)
                {
                    _logger.Warn($"数据验证失败，跳过插入: {data} - {validation.GetSummary()}");
                    return false;
                }

                // 检查重复
                if (await CheckDuplicateAsync(data.DeviceName, data.ReportDateTime))
                {
                    _logger.Info($"数据已存在，跳过插入: {data.DeviceName} - {data.ReportDateTime:yyyy-MM-dd HH:mm:ss}");
                    return false;
                }

                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    
                    using (var command = new SqlCommand(INSERT_SQL, connection))
                    {
                        AddParameters(command, data);
                        
                        var rowsAffected = await command.ExecuteNonQueryAsync();
                        var success = rowsAffected > 0;

                        if (success)
                        {
                            _logger.Info($"成功插入数据: {data.DeviceName} - {data.ReportDateTime:yyyy-MM-dd HH:mm:ss}");
                        }
                        else
                        {
                            _logger.Warn($"插入数据失败，无行受影响: {data}");
                        }

                        return success;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"插入数据异常: {data} - {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 批量插入报告数据
        /// </summary>
        /// <param name="dataList">报告数据列表</param>
        /// <returns>成功插入的记录数</returns>
        public async Task<int> InsertBatchReportDataAsync(IEnumerable<ExcelReportData> dataList)
        {
            if (dataList == null)
                throw new ArgumentNullException(nameof(dataList));

            var dataArray = dataList.ToArray();
            if (dataArray.Length == 0)
            {
                _logger.Info("批量插入：数据列表为空");
                return 0;
            }

            _logger.Info($"开始批量插入数据，记录数: {dataArray.Length}");

            int successCount = 0;
            int duplicateCount = 0;
            int errorCount = 0;

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    // 使用事务确保数据一致性
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            foreach (var data in dataArray)
                            {
                                try
                                {
                                    // 验证数据
                                    var validation = data.Validate();
                                    if (!validation.IsValid)
                                    {
                                        _logger.Warn($"数据验证失败，跳过: {data} - {validation.GetSummary()}");
                                        errorCount++;
                                        continue;
                                    }

                                    // 检查重复
                                    if (await CheckDuplicateInTransactionAsync(connection, transaction, data.DeviceName, data.ReportDateTime))
                                    {
                                        _logger.Debug($"数据已存在，跳过: {data.DeviceName} - {data.ReportDateTime:yyyy-MM-dd HH:mm:ss}");
                                        duplicateCount++;
                                        continue;
                                    }

                                    // 插入数据
                                    using (var command = new SqlCommand(INSERT_SQL, connection, transaction))
                                    {
                                        AddParameters(command, data);
                                        var rowsAffected = await command.ExecuteNonQueryAsync();
                                        
                                        if (rowsAffected > 0)
                                        {
                                            successCount++;
                                        }
                                        else
                                        {
                                            errorCount++;
                                            _logger.Warn($"插入失败，无行受影响: {data}");
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    errorCount++;
                                    _logger.Error($"插入单条记录异常: {data} - {ex.Message}", ex);
                                }
                            }

                            // 提交事务
                            transaction.Commit();
                            _logger.Info($"批量插入完成 - 成功: {successCount}, 重复: {duplicateCount}, 错误: {errorCount}");
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            _logger.Error($"批量插入事务失败，已回滚: {ex.Message}", ex);
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"批量插入异常: {ex.Message}", ex);
                throw;
            }

            return successCount;
        }

        /// <summary>
        /// 检查数据是否重复
        /// </summary>
        /// <param name="deviceName">设备名称</param>
        /// <param name="reportDateTime">报告时间</param>
        /// <returns>是否重复</returns>
        public async Task<bool> CheckDuplicateAsync(string deviceName, DateTime reportDateTime)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    
                    using (var command = new SqlCommand(CHECK_DUPLICATE_SQL, connection))
                    {
                        command.Parameters.AddWithValue("@DeviceName", deviceName ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@ReportDateTime", reportDateTime);
                        
                        var count = (int)await command.ExecuteScalarAsync();
                        return count > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"检查重复数据异常: {deviceName} - {reportDateTime} - {ex.Message}", ex);
                return false; // 出错时假设不重复，允许插入
            }
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        /// <returns>连接是否成功</returns>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    _logger.Info("数据库连接测试成功");
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"数据库连接测试失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取今日记录数量
        /// </summary>
        /// <returns>记录数量</returns>
        public async Task<int> GetTodayRecordCountAsync()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    
                    using (var command = new SqlCommand(COUNT_TODAY_SQL, connection))
                    {
                        var count = (int)await command.ExecuteScalarAsync();
                        return count;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"获取今日记录数量异常: {ex.Message}", ex);
                return 0;
            }
        }

        /// <summary>
        /// 在事务中检查重复数据
        /// </summary>
        private async Task<bool> CheckDuplicateInTransactionAsync(SqlConnection connection, SqlTransaction transaction, 
            string deviceName, DateTime reportDateTime)
        {
            using (var command = new SqlCommand(CHECK_DUPLICATE_SQL, connection, transaction))
            {
                command.Parameters.AddWithValue("@DeviceName", deviceName ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ReportDateTime", reportDateTime);
                
                var count = (int)await command.ExecuteScalarAsync();
                return count > 0;
            }
        }

        /// <summary>
        /// 添加SQL参数
        /// </summary>
        private void AddParameters(SqlCommand command, ExcelReportData data)
        {
            command.Parameters.AddWithValue("@DeviceName", data.DeviceName ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@ReportDateTime", data.ReportDateTime);
            command.Parameters.AddWithValue("@SourceFilePath", data.SourceFilePath ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@ProcessedBy", "DailyReportExtractor");

            // AGA10结果参数
            var aga10 = data.AGA10Results;
            command.Parameters.AddWithValue("@Press", aga10.Press ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Temp", aga10.Temp ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@C1", aga10.C1 ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@C2", aga10.C2 ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@C3", aga10.C3 ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@nC4", aga10.nC4 ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@iC4", aga10.iC4 ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@nC5", aga10.nC5 ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@iC5", aga10.iC5 ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@neoC5", aga10.neoC5 ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@C6", aga10.C6 ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@N2", aga10.N2 ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@CO2", aga10.CO2 ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Total", aga10.Total ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@AvgSOS", aga10.AvgSOS ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@CalculateSOS", aga10.CalculateSOS ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Dev", aga10.Dev ?? (object)DBNull.Value);
        }
    }
}
