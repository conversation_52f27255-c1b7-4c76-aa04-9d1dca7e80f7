using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using OfficeOpenXml;
using FlowCheck_DPLNG.DailyReportExtractor.Models;
using log4net;

namespace FlowCheck_DPLNG.DailyReportExtractor.ExcelProcessing
{
    /// <summary>
    /// Excel数据提取器接口
    /// </summary>
    public interface IExcelDataExtractor
    {
        Task<ExcelReportData> ExtractDataAsync(string filePath);
        Task<List<ExcelReportData>> ExtractBatchDataAsync(IEnumerable<string> filePaths);
        bool ValidateExcelFile(string filePath);
    }

    /// <summary>
    /// Excel数据提取器实现
    /// 负责从Excel文件中提取SOS检查报告数据
    /// </summary>
    public class ExcelDataExtractor : IExcelDataExtractor
    {
        private readonly string _excelPassword;
        private static readonly ILog _logger = LogManager.GetLogger(typeof(ExcelDataExtractor));

        // 工作表名称常量
        private const string INSPECTION_REPORT_SHEET = "Inspection Report";
        private const string AGA10_RESULT_SHEET = "AGA10 Result";

        // 单元格位置常量
        private const string DEVICE_NAME_CELL = "B2";
        private const string REPORT_DATE_CELL = "K1";
        private const string REPORT_TIME_CELL = "K2";

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="excelPassword">Excel工作表保护密码</param>
        public ExcelDataExtractor(string excelPassword = "Admin123")
        {
            _excelPassword = excelPassword ?? "Admin123";

            // 设置EPPlus许可证
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        /// <summary>
        /// 从单个Excel文件提取数据
        /// </summary>
        /// <param name="filePath">Excel文件路径</param>
        /// <returns>提取的报告数据</returns>
        public async Task<ExcelReportData> ExtractDataAsync(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            if (!File.Exists(filePath))
                throw new FileNotFoundException($"文件不存在: {filePath}");

            _logger.Info($"开始提取Excel数据: {filePath}");

            try
            {
                using (var package = new ExcelPackage(new FileInfo(filePath)))
                {
                    var reportData = new ExcelReportData
                    {
                        SourceFilePath = filePath
                    };

                    // 提取基本信息
                    await ExtractBasicInfoAsync(package, reportData);

                    // 提取AGA10计算结果
                    await ExtractAGA10ResultsAsync(package, reportData);

                    // 验证数据
                    var validation = reportData.Validate();
                    if (!validation.IsValid)
                    {
                        var errorMsg = $"数据验证失败: {string.Join(", ", validation.Errors.Select(e => e.ToString()))}";
                        _logger.Warn($"{filePath} - {errorMsg}");
                    }

                    if (validation.Warnings.Any())
                    {
                        var warningMsg = $"数据警告: {string.Join(", ", validation.Warnings.Select(w => w.ToString()))}";
                        _logger.Warn($"{filePath} - {warningMsg}");
                    }

                    _logger.Info($"成功提取Excel数据: {filePath} - {reportData}");
                    return reportData;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"提取Excel数据失败: {filePath} - {ex.Message}", ex);
                throw new InvalidOperationException($"提取Excel数据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 批量提取Excel数据
        /// </summary>
        /// <param name="filePaths">Excel文件路径集合</param>
        /// <returns>提取的报告数据列表</returns>
        public async Task<List<ExcelReportData>> ExtractBatchDataAsync(IEnumerable<string> filePaths)
        {
            if (filePaths == null)
                throw new ArgumentNullException(nameof(filePaths));

            var fileList = filePaths.ToList();
            _logger.Info($"开始批量提取Excel数据，文件数量: {fileList.Count}");

            var results = new List<ExcelReportData>();
            var errors = new List<string>();

            foreach (var filePath in fileList)
            {
                try
                {
                    var data = await ExtractDataAsync(filePath);
                    results.Add(data);
                }
                catch (Exception ex)
                {
                    var errorMsg = $"文件 {filePath} 处理失败: {ex.Message}";
                    errors.Add(errorMsg);
                    _logger.Error(errorMsg, ex);
                }
            }

            _logger.Info($"批量提取完成 - 成功: {results.Count}, 失败: {errors.Count}");

            if (errors.Any())
            {
                _logger.Warn($"批量提取存在错误: {string.Join("; ", errors)}");
            }

            return results;
        }

        /// <summary>
        /// 验证Excel文件是否有效
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否有效</returns>
        public bool ValidateExcelFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return false;

                var fileInfo = new FileInfo(filePath);
                if (fileInfo.Length == 0)
                    return false;

                // 检查文件扩展名
                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                if (extension != ".xlsx" && extension != ".xls")
                    return false;

                // 尝试打开文件
                using (var package = new ExcelPackage(fileInfo))
                {
                    // 检查必需的工作表是否存在
                    var hasInspectionSheet = package.Workbook.Worksheets.Any(ws => 
                        ws.Name.Equals(INSPECTION_REPORT_SHEET, StringComparison.OrdinalIgnoreCase));
                    var hasAGA10Sheet = package.Workbook.Worksheets.Any(ws => 
                        ws.Name.Equals(AGA10_RESULT_SHEET, StringComparison.OrdinalIgnoreCase));

                    return hasInspectionSheet && hasAGA10Sheet;
                }
            }
            catch (Exception ex)
            {
                _logger.Debug($"文件验证失败: {filePath} - {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 提取基本信息（从Inspection Report工作表）
        /// </summary>
        private async Task ExtractBasicInfoAsync(ExcelPackage package, ExcelReportData reportData)
        {
            var worksheet = GetWorksheet(package, INSPECTION_REPORT_SHEET);
            
            // 解除工作表保护
            if (worksheet.Protection.IsProtected)
            {
                try
                {
                    worksheet.Protection.SetPassword(_excelPassword);
                    _logger.Debug($"成功解除工作表保护: {INSPECTION_REPORT_SHEET}");
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"解除工作表保护失败: {ex.Message}", ex);
                }
            }

            // 提取设备名称 (B2)
            var deviceNameValue = worksheet.Cells[DEVICE_NAME_CELL].Value;
            reportData.DeviceName = deviceNameValue?.ToString()?.Trim();

            // 提取报告日期 (K1)
            var reportDateValue = worksheet.Cells[REPORT_DATE_CELL].Value;
            
            // 提取报告时间 (K2)
            var reportTimeValue = worksheet.Cells[REPORT_TIME_CELL].Value;

            // 合并日期和时间
            reportData.ReportDateTime = CombineDateAndTime(reportDateValue, reportTimeValue);

            _logger.Debug($"基本信息提取完成 - 设备: {reportData.DeviceName}, 时间: {reportData.ReportDateTime}");
        }

        /// <summary>
        /// 提取AGA10计算结果（从AGA10 Result工作表）
        /// </summary>
        private async Task ExtractAGA10ResultsAsync(ExcelPackage package, ExcelReportData reportData)
        {
            var worksheet = GetWorksheet(package, AGA10_RESULT_SHEET);
            
            // 解除工作表保护
            if (worksheet.Protection.IsProtected)
            {
                try
                {
                    worksheet.Protection.SetPassword(_excelPassword);
                    _logger.Debug($"成功解除工作表保护: {AGA10_RESULT_SHEET}");
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"解除工作表保护失败: {ex.Message}", ex);
                }
            }

            var aga10Data = new AGA10ResultData();

            // 按照需求文档中的单元格映射提取数据
            aga10Data.Press = GetDecimalValue(worksheet, "B2");
            aga10Data.Temp = GetDecimalValue(worksheet, "B3");
            aga10Data.C1 = GetDecimalValue(worksheet, "B5");
            aga10Data.C2 = GetDecimalValue(worksheet, "B6");
            aga10Data.C3 = GetDecimalValue(worksheet, "B7");
            aga10Data.nC4 = GetDecimalValue(worksheet, "B8");
            aga10Data.iC4 = GetDecimalValue(worksheet, "B9");
            aga10Data.nC5 = GetDecimalValue(worksheet, "B10");
            aga10Data.iC5 = GetDecimalValue(worksheet, "B11");
            aga10Data.neoC5 = GetDecimalValue(worksheet, "B12");
            aga10Data.C6 = GetDecimalValue(worksheet, "B13");
            aga10Data.N2 = GetDecimalValue(worksheet, "B14");
            aga10Data.CO2 = GetDecimalValue(worksheet, "B15");
            aga10Data.Total = GetDecimalValue(worksheet, "B16");
            aga10Data.AvgSOS = GetDecimalValue(worksheet, "B18");
            aga10Data.CalculateSOS = GetDecimalValue(worksheet, "B19");
            aga10Data.Dev = GetDecimalValue(worksheet, "B20");

            reportData.AGA10Results = aga10Data;

            _logger.Debug($"AGA10结果提取完成 - 有效数据项: {aga10Data.ToValueDictionary().Count}");
        }

        /// <summary>
        /// 获取工作表
        /// </summary>
        private ExcelWorksheet GetWorksheet(ExcelPackage package, string sheetName)
        {
            var worksheet = package.Workbook.Worksheets.FirstOrDefault(ws => 
                ws.Name.Equals(sheetName, StringComparison.OrdinalIgnoreCase));

            if (worksheet == null)
            {
                throw new InvalidOperationException($"找不到工作表: {sheetName}");
            }

            return worksheet;
        }

        /// <summary>
        /// 获取单元格的decimal值
        /// </summary>
        private decimal? GetDecimalValue(ExcelWorksheet worksheet, string cellAddress)
        {
            try
            {
                var cellValue = worksheet.Cells[cellAddress].Value;
                if (cellValue == null)
                    return null;

                if (decimal.TryParse(cellValue.ToString(), out decimal result))
                    return result;

                if (double.TryParse(cellValue.ToString(), out double doubleResult))
                    return (decimal)doubleResult;

                return null;
            }
            catch (Exception ex)
            {
                _logger.Debug($"获取单元格值失败 {cellAddress}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 合并日期和时间
        /// </summary>
        private DateTime CombineDateAndTime(object dateValue, object timeValue)
        {
            try
            {
                DateTime date = DateTime.MinValue;
                TimeSpan time = TimeSpan.Zero;

                // 解析日期
                if (dateValue is DateTime dateTime)
                {
                    date = dateTime.Date;
                }
                else if (dateValue != null && DateTime.TryParse(dateValue.ToString(), out DateTime parsedDate))
                {
                    date = parsedDate.Date;
                }

                // 解析时间
                if (timeValue is DateTime timeDateTime)
                {
                    time = timeDateTime.TimeOfDay;
                }
                else if (timeValue is TimeSpan timeSpan)
                {
                    time = timeSpan;
                }
                else if (timeValue != null && TimeSpan.TryParse(timeValue.ToString(), out TimeSpan parsedTime))
                {
                    time = parsedTime;
                }

                if (date == DateTime.MinValue)
                {
                    throw new InvalidOperationException("无法解析报告日期");
                }

                return date.Add(time);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"合并日期时间失败: {ex.Message}", ex);
            }
        }
    }
}
