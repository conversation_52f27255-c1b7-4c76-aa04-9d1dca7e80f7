-- =============================================
-- 每日报告数据提取器 - SOSCheckResult表创建脚本
-- 创建日期: 2024-01-15
-- 描述: 用于存储从Excel报告中提取的SOS检查结果数据
-- =============================================

USE [DaPeng_IOServer]
GO

-- 检查表是否存在，如果存在则删除（仅用于开发阶段）
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SOSCheckResult]') AND type in (N'U'))
BEGIN
    PRINT '表 SOSCheckResult 已存在，正在删除...'
    DROP TABLE [dbo].[SOSCheckResult]
    PRINT '表 SOSCheckResult 已删除'
END
GO

-- 创建SOSCheckResult表
CREATE TABLE [dbo].[SOSCheckResult](
    -- 主键：自增整数
    [ID] [int] IDENTITY(1,1) NOT NULL,
    
    -- 基本信息字段（来自Inspection Report工作表）
    [DeviceName] [nvarchar](255) NOT NULL,
    [ReportDateTime] [datetime2](3) NOT NULL,
    
    -- AGA10计算结果字段（来自AGA10 Result工作表）
    [Press] [decimal](18, 4) NULL,
    [Temp] [decimal](18, 4) NULL,
    [C1] [decimal](18, 4) NULL,
    [C2] [decimal](18, 4) NULL,
    [C3] [decimal](18, 4) NULL,
    [nC4] [decimal](18, 4) NULL,
    [iC4] [decimal](18, 4) NULL,
    [nC5] [decimal](18, 4) NULL,
    [iC5] [decimal](18, 4) NULL,
    [neoC5] [decimal](18, 4) NULL,
    [C6] [decimal](18, 4) NULL,
    [N2] [decimal](18, 4) NULL,
    [CO2] [decimal](18, 4) NULL,
    [Total] [decimal](18, 4) NULL,
    [AvgSOS] [decimal](18, 4) NULL,
    [CalculateSOS] [decimal](18, 4) NULL,
    [Dev] [decimal](18, 4) NULL,
    
    -- 系统字段
    [RecordCreatedAt] [datetime2](3) NOT NULL DEFAULT GETDATE(),
    [SourceFilePath] [nvarchar](500) NULL,
    [ProcessedBy] [nvarchar](100) NULL DEFAULT 'DailyReportExtractor',
    
    -- 主键约束
    CONSTRAINT [PK_SOSCheckResult] PRIMARY KEY CLUSTERED ([ID] ASC)
        WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, 
              ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- 创建唯一索引，防止重复数据（基于设备名和报告时间）
CREATE UNIQUE NONCLUSTERED INDEX [IX_SOSCheckResult_DeviceName_ReportDateTime] 
ON [dbo].[SOSCheckResult] ([DeviceName] ASC, [ReportDateTime] ASC)
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
      IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, 
      ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO

-- 创建设备名索引，提高查询性能
CREATE NONCLUSTERED INDEX [IX_SOSCheckResult_DeviceName] 
ON [dbo].[SOSCheckResult] ([DeviceName] ASC)
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
      DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, 
      ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO

-- 创建报告时间索引，提高按时间查询的性能
CREATE NONCLUSTERED INDEX [IX_SOSCheckResult_ReportDateTime] 
ON [dbo].[SOSCheckResult] ([ReportDateTime] ASC)
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
      DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, 
      ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO

-- 创建记录创建时间索引
CREATE NONCLUSTERED INDEX [IX_SOSCheckResult_RecordCreatedAt] 
ON [dbo].[SOSCheckResult] ([RecordCreatedAt] ASC)
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
      DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, 
      ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO

-- 添加表注释
EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'SOS检查结果表，存储从Excel报告中提取的每日检查数据', 
    @level0type=N'SCHEMA', @level0name=N'dbo', 
    @level1type=N'TABLE', @level1name=N'SOSCheckResult'
GO

-- 添加字段注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键，自增整数' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SOSCheckResult', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'设备名称，来自Inspection Report工作表B2单元格' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SOSCheckResult', @level2type=N'COLUMN',@level2name=N'DeviceName'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'报告生成时间，由K1和K2单元格合并而成' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SOSCheckResult', @level2type=N'COLUMN',@level2name=N'ReportDateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'压力值，来自AGA10 Result工作表B2单元格' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SOSCheckResult', @level2type=N'COLUMN',@level2name=N'Press'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'温度值，来自AGA10 Result工作表B3单元格' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SOSCheckResult', @level2type=N'COLUMN',@level2name=N'Temp'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'记录创建时间，系统自动生成' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SOSCheckResult', @level2type=N'COLUMN',@level2name=N'RecordCreatedAt'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'源文件路径，用于追溯数据来源' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SOSCheckResult', @level2type=N'COLUMN',@level2name=N'SourceFilePath'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'处理程序标识' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SOSCheckResult', @level2type=N'COLUMN',@level2name=N'ProcessedBy'
GO

PRINT '表 SOSCheckResult 创建成功！'
PRINT '包含以下功能：'
PRINT '- 主键自增ID'
PRINT '- 设备名称和报告时间的唯一约束'
PRINT '- 17个AGA10计算结果字段'
PRINT '- 系统字段（创建时间、源文件路径等）'
PRINT '- 性能优化索引'
PRINT '- 完整的字段注释'
GO

-- 插入测试数据（可选）
/*
INSERT INTO [dbo].[SOSCheckResult] 
    ([DeviceName], [ReportDateTime], [Press], [Temp], [C1], [C2], [C3], [SourceFilePath])
VALUES 
    ('FT001', '2024-01-15 08:30:00', 25.5, 15.2, 85.5, 10.2, 3.1, 'D:\Report\Station1\FT001\SOSCheckReport\GUSM_AutoSOSCHECK_FT001_2024-01-15 08-30-00.xlsx')
GO
*/
