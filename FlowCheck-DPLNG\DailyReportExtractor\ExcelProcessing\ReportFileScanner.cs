using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using log4net;

namespace FlowCheck_DPLNG.DailyReportExtractor.ExcelProcessing
{
    /// <summary>
    /// 报告文件扫描器接口
    /// </summary>
    public interface IReportFileScanner
    {
        Task<IEnumerable<string>> ScanTodayReportsAsync(string rootPath);
        Task<IEnumerable<string>> ScanReportsByDateAsync(string rootPath, DateTime date);
        bool IsValidReportFile(string filePath);
        Task<IEnumerable<string>> ScanAllReportsAsync(string rootPath);
    }

    /// <summary>
    /// 报告文件扫描器实现
    /// 负责扫描指定目录下的Excel报告文件
    /// </summary>
    public class ReportFileScanner : IReportFileScanner
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(ReportFileScanner));

        // 文件扩展名过滤
        private readonly string[] _validExtensions = { ".xlsx", ".xls" };
        
        // 文件名模式（基于现有系统的命名规则）
        private readonly string _fileNamePattern;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="fileNamePattern">文件名模式，如"GUSM_AutoSOSCHECK"</param>
        public ReportFileScanner(string fileNamePattern = "GUSM_AutoSOSCHECK")
        {
            _fileNamePattern = fileNamePattern ?? "GUSM_AutoSOSCHECK";
        }

        /// <summary>
        /// 扫描今日的报告文件
        /// </summary>
        /// <param name="rootPath">根目录路径</param>
        /// <returns>今日报告文件路径列表</returns>
        public async Task<IEnumerable<string>> ScanTodayReportsAsync(string rootPath)
        {
            return await ScanReportsByDateAsync(rootPath, DateTime.Today);
        }

        /// <summary>
        /// 扫描指定日期的报告文件
        /// </summary>
        /// <param name="rootPath">根目录路径</param>
        /// <param name="date">指定日期</param>
        /// <returns>指定日期的报告文件路径列表</returns>
        public async Task<IEnumerable<string>> ScanReportsByDateAsync(string rootPath, DateTime date)
        {
            if (string.IsNullOrWhiteSpace(rootPath))
                throw new ArgumentException("根目录路径不能为空", nameof(rootPath));

            if (!Directory.Exists(rootPath))
            {
                _logger.Warn($"根目录不存在: {rootPath}");
                return Enumerable.Empty<string>();
            }

            _logger.Info($"开始扫描 {date:yyyy-MM-dd} 的报告文件，根目录: {rootPath}");

            try
            {
                var reportFiles = new List<string>();
                var dateString = date.ToString("yyyy-MM-dd");

                // 递归扫描所有子目录
                await ScanDirectoryRecursiveAsync(rootPath, dateString, reportFiles);

                _logger.Info($"扫描完成，找到 {reportFiles.Count} 个 {date:yyyy-MM-dd} 的报告文件");

                return reportFiles.OrderBy(f => f);
            }
            catch (Exception ex)
            {
                _logger.Error($"扫描报告文件异常: {rootPath} - {ex.Message}", ex);
                return Enumerable.Empty<string>();
            }
        }

        /// <summary>
        /// 扫描所有报告文件（不限日期）
        /// </summary>
        /// <param name="rootPath">根目录路径</param>
        /// <returns>所有报告文件路径列表</returns>
        public async Task<IEnumerable<string>> ScanAllReportsAsync(string rootPath)
        {
            if (string.IsNullOrWhiteSpace(rootPath))
                throw new ArgumentException("根目录路径不能为空", nameof(rootPath));

            if (!Directory.Exists(rootPath))
            {
                _logger.Warn($"根目录不存在: {rootPath}");
                return Enumerable.Empty<string>();
            }

            _logger.Info($"开始扫描所有报告文件，根目录: {rootPath}");

            try
            {
                var reportFiles = new List<string>();

                // 递归扫描所有子目录
                await ScanDirectoryRecursiveAsync(rootPath, null, reportFiles);

                _logger.Info($"扫描完成，找到 {reportFiles.Count} 个报告文件");

                return reportFiles.OrderBy(f => f);
            }
            catch (Exception ex)
            {
                _logger.Error($"扫描所有报告文件异常: {rootPath} - {ex.Message}", ex);
                return Enumerable.Empty<string>();
            }
        }

        /// <summary>
        /// 验证文件是否为有效的报告文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否有效</returns>
        public bool IsValidReportFile(string filePath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath) || !File.Exists(filePath))
                    return false;

                var fileName = Path.GetFileName(filePath);
                var extension = Path.GetExtension(filePath).ToLowerInvariant();

                // 检查文件扩展名
                if (!_validExtensions.Contains(extension))
                    return false;

                // 检查文件名模式
                if (!fileName.StartsWith(_fileNamePattern, StringComparison.OrdinalIgnoreCase))
                    return false;

                // 检查文件大小（避免空文件或损坏文件）
                var fileInfo = new FileInfo(filePath);
                if (fileInfo.Length < 1024) // 小于1KB的文件可能有问题
                    return false;

                // 检查文件是否被占用
                if (IsFileLocked(filePath))
                    return false;

                return true;
            }
            catch (Exception ex)
            {
                _logger.Debug($"验证报告文件失败: {filePath} - {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 递归扫描目录
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="dateFilter">日期过滤器（null表示不过滤）</param>
        /// <param name="results">结果列表</param>
        private async Task ScanDirectoryRecursiveAsync(string directoryPath, string dateFilter, List<string> results)
        {
            try
            {
                // 扫描当前目录的文件
                var files = Directory.GetFiles(directoryPath, "*.xlsx", SearchOption.TopDirectoryOnly)
                    .Concat(Directory.GetFiles(directoryPath, "*.xls", SearchOption.TopDirectoryOnly));

                foreach (var file in files)
                {
                    if (IsValidReportFile(file))
                    {
                        // 如果指定了日期过滤器，检查文件名是否包含该日期
                        if (dateFilter == null || IsFileFromDate(file, dateFilter))
                        {
                            results.Add(file);
                            _logger.Debug($"找到报告文件: {file}");
                        }
                    }
                }

                // 递归扫描子目录
                var subdirectories = Directory.GetDirectories(directoryPath);
                foreach (var subdirectory in subdirectories)
                {
                    // 跳过系统目录和隐藏目录
                    var dirInfo = new DirectoryInfo(subdirectory);
                    if (dirInfo.Attributes.HasFlag(FileAttributes.Hidden) || 
                        dirInfo.Attributes.HasFlag(FileAttributes.System))
                        continue;

                    await ScanDirectoryRecursiveAsync(subdirectory, dateFilter, results);
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.Warn($"无权限访问目录: {directoryPath} - {ex.Message}");
            }
            catch (Exception ex)
            {
                _logger.Error($"扫描目录异常: {directoryPath} - {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 检查文件是否来自指定日期
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="dateString">日期字符串 (yyyy-MM-dd)</param>
        /// <returns>是否匹配</returns>
        private bool IsFileFromDate(string filePath, string dateString)
        {
            try
            {
                var fileName = Path.GetFileName(filePath);
                
                // 检查文件名是否包含日期字符串
                if (fileName.Contains(dateString))
                    return true;

                // 检查文件的创建时间或修改时间
                var fileInfo = new FileInfo(filePath);
                var fileDate = fileInfo.CreationTime.Date;
                var targetDate = DateTime.ParseExact(dateString, "yyyy-MM-dd", null).Date;

                return fileDate == targetDate;
            }
            catch (Exception ex)
            {
                _logger.Debug($"检查文件日期失败: {filePath} - {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查文件是否被占用
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否被占用</returns>
        private bool IsFileLocked(string filePath)
        {
            try
            {
                using (var stream = File.Open(filePath, FileMode.Open, FileAccess.Read, FileShare.None))
                {
                    return false;
                }
            }
            catch (IOException)
            {
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 获取目录统计信息
        /// </summary>
        /// <param name="rootPath">根目录路径</param>
        /// <returns>统计信息</returns>
        public async Task<ScanStatistics> GetScanStatisticsAsync(string rootPath)
        {
            var stats = new ScanStatistics();

            try
            {
                if (!Directory.Exists(rootPath))
                    return stats;

                var allFiles = await ScanAllReportsAsync(rootPath);
                stats.TotalFiles = allFiles.Count();

                var todayFiles = await ScanTodayReportsAsync(rootPath);
                stats.TodayFiles = todayFiles.Count();

                // 按日期分组统计
                var filesByDate = new Dictionary<DateTime, int>();
                foreach (var file in allFiles)
                {
                    var fileInfo = new FileInfo(file);
                    var date = fileInfo.CreationTime.Date;
                    
                    if (filesByDate.ContainsKey(date))
                        filesByDate[date]++;
                    else
                        filesByDate[date] = 1;
                }

                stats.FilesByDate = filesByDate;
            }
            catch (Exception ex)
            {
                _logger.Error($"获取扫描统计信息异常: {rootPath} - {ex.Message}", ex);
            }

            return stats;
        }
    }

    /// <summary>
    /// 扫描统计信息
    /// </summary>
    public class ScanStatistics
    {
        public int TotalFiles { get; set; }
        public int TodayFiles { get; set; }
        public Dictionary<DateTime, int> FilesByDate { get; set; }

        public ScanStatistics()
        {
            FilesByDate = new Dictionary<DateTime, int>();
        }

        public override string ToString()
        {
            return $"Total: {TotalFiles}, Today: {TodayFiles}, Dates: {FilesByDate.Count}";
        }
    }
}
