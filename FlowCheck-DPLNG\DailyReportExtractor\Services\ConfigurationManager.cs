using System;
using System.IO;
using System.Xml;
using log4net;

namespace FlowCheck_DPLNG.DailyReportExtractor.Services
{
    /// <summary>
    /// 配置管理器接口
    /// </summary>
    public interface IConfigurationManager
    {
        DailyExtractorConfig LoadConfiguration();
        void SaveConfiguration(DailyExtractorConfig config);
        bool ConfigurationExists();
        void CreateDefaultConfiguration();
    }

    /// <summary>
    /// 每日提取器配置
    /// </summary>
    public class DailyExtractorConfig
    {
        public TimeSpan ScheduleTime { get; set; } = new TimeSpan(7, 0, 0); // 默认7:00
        public bool Enabled { get; set; } = true;
        public string ReportRootPath { get; set; } = @"D:\Report\";
        public string ExcelPassword { get; set; } = "Admin123";
        public string FileNamePattern { get; set; } = "GUSM_AutoSOSCHECK";
        public string DatabaseConnectionString { get; set; }
        public string DatabaseTableName { get; set; } = "SOSCheckResult";
        public bool EnableBatchInsert { get; set; } = true;
        public int BatchSize { get; set; } = 100;
        public bool EnableDetailedLogging { get; set; } = true;
        public string LogLevel { get; set; } = "Info";
        public string LogFilePath { get; set; } = @"Logs\DailyExtractor.log";

        /// <summary>
        /// 验证配置
        /// </summary>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(ReportRootPath))
                result.AddError("ReportRootPath", "报告根目录路径不能为空");

            if (string.IsNullOrWhiteSpace(DatabaseConnectionString))
                result.AddError("DatabaseConnectionString", "数据库连接字符串不能为空");

            if (string.IsNullOrWhiteSpace(ExcelPassword))
                result.AddError("ExcelPassword", "Excel密码不能为空");

            if (BatchSize <= 0 || BatchSize > 1000)
                result.AddError("BatchSize", "批量大小必须在1-1000之间");

            return result;
        }
    }

    /// <summary>
    /// 配置管理器实现
    /// </summary>
    public class ConfigurationManager : IConfigurationManager
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(ConfigurationManager));
        private readonly string _configFilePath;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="configFilePath">配置文件路径</param>
        public ConfigurationManager(string configFilePath = null)
        {
            _configFilePath = configFilePath ?? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, 
                "DailyReportExtractor", "Config", "DailyExtractorConfig.xml");
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        public DailyExtractorConfig LoadConfiguration()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    _logger.Warn($"配置文件不存在: {_configFilePath}，将创建默认配置");
                    CreateDefaultConfiguration();
                }

                _logger.Info($"加载配置文件: {_configFilePath}");

                var config = new DailyExtractorConfig();
                var doc = new XmlDocument();
                doc.Load(_configFilePath);

                // 读取调度配置
                var scheduleNode = doc.SelectSingleNode("/DailyExtractorConfiguration/Schedule");
                if (scheduleNode != null)
                {
                    var executionTimeText = GetNodeValue(scheduleNode, "ExecutionTime", "07:00:00");
                    if (TimeSpan.TryParse(executionTimeText, out TimeSpan scheduleTime))
                    {
                        config.ScheduleTime = scheduleTime;
                    }

                    config.Enabled = GetBoolValue(scheduleNode, "Enabled", true);
                }

                // 读取报告设置
                var reportNode = doc.SelectSingleNode("/DailyExtractorConfiguration/ReportSettings");
                if (reportNode != null)
                {
                    config.ReportRootPath = GetNodeValue(reportNode, "RootPath", @"D:\Report\");
                    config.ExcelPassword = GetNodeValue(reportNode, "ExcelPassword", "Admin123");
                    config.FileNamePattern = GetNodeValue(reportNode, "FileNamePattern", "GUSM_AutoSOSCHECK");
                }

                // 读取数据库设置
                var dbNode = doc.SelectSingleNode("/DailyExtractorConfiguration/DatabaseSettings");
                if (dbNode != null)
                {
                    config.DatabaseConnectionString = GetNodeValue(dbNode, "ConnectionString", "");
                    config.DatabaseTableName = GetNodeValue(dbNode, "TableName", "SOSCheckResult");
                    config.EnableBatchInsert = GetBoolValue(dbNode, "EnableBatchInsert", true);
                    config.BatchSize = GetIntValue(dbNode, "BatchSize", 100);
                }

                // 读取日志设置
                var logNode = doc.SelectSingleNode("/DailyExtractorConfiguration/LoggingSettings");
                if (logNode != null)
                {
                    config.EnableDetailedLogging = GetBoolValue(logNode, "EnableDetailedLogging", true);
                    config.LogLevel = GetNodeValue(logNode, "LogLevel", "Info");
                    config.LogFilePath = GetNodeValue(logNode, "LogFilePath", @"Logs\DailyExtractor.log");
                }

                // 如果数据库连接字符串为空，尝试从主配置文件读取
                if (string.IsNullOrWhiteSpace(config.DatabaseConnectionString))
                {
                    config.DatabaseConnectionString = LoadDatabaseConnectionFromMainConfig();
                }

                // 验证配置
                var validation = config.Validate();
                if (!validation.IsValid)
                {
                    var errors = string.Join(", ", validation.Errors);
                    _logger.Error($"配置验证失败: {errors}");
                    throw new InvalidOperationException($"配置验证失败: {errors}");
                }

                _logger.Info("配置加载成功");
                return config;
            }
            catch (Exception ex)
            {
                _logger.Error($"加载配置失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        public void SaveConfiguration(DailyExtractorConfig config)
        {
            if (config == null)
                throw new ArgumentNullException(nameof(config));

            try
            {
                _logger.Info($"保存配置到: {_configFilePath}");

                // 确保目录存在
                var directory = Path.GetDirectoryName(_configFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var doc = new XmlDocument();
                doc.LoadXml(GetDefaultConfigXml());

                // 更新调度配置
                var scheduleNode = doc.SelectSingleNode("/DailyExtractorConfiguration/Schedule");
                SetNodeValue(scheduleNode, "ExecutionTime", config.ScheduleTime.ToString(@"hh\:mm\:ss"));
                SetNodeValue(scheduleNode, "Enabled", config.Enabled.ToString().ToLower());

                // 更新报告设置
                var reportNode = doc.SelectSingleNode("/DailyExtractorConfiguration/ReportSettings");
                SetNodeValue(reportNode, "RootPath", config.ReportRootPath);
                SetNodeValue(reportNode, "ExcelPassword", config.ExcelPassword);
                SetNodeValue(reportNode, "FileNamePattern", config.FileNamePattern);

                // 更新数据库设置
                var dbNode = doc.SelectSingleNode("/DailyExtractorConfiguration/DatabaseSettings");
                SetNodeValue(dbNode, "ConnectionString", config.DatabaseConnectionString);
                SetNodeValue(dbNode, "TableName", config.DatabaseTableName);
                SetNodeValue(dbNode, "EnableBatchInsert", config.EnableBatchInsert.ToString().ToLower());
                SetNodeValue(dbNode, "BatchSize", config.BatchSize.ToString());

                // 更新日志设置
                var logNode = doc.SelectSingleNode("/DailyExtractorConfiguration/LoggingSettings");
                SetNodeValue(logNode, "EnableDetailedLogging", config.EnableDetailedLogging.ToString().ToLower());
                SetNodeValue(logNode, "LogLevel", config.LogLevel);
                SetNodeValue(logNode, "LogFilePath", config.LogFilePath);

                doc.Save(_configFilePath);
                _logger.Info("配置保存成功");
            }
            catch (Exception ex)
            {
                _logger.Error($"保存配置失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 检查配置文件是否存在
        /// </summary>
        public bool ConfigurationExists()
        {
            return File.Exists(_configFilePath);
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        public void CreateDefaultConfiguration()
        {
            try
            {
                _logger.Info($"创建默认配置文件: {_configFilePath}");

                var directory = Path.GetDirectoryName(_configFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var defaultConfig = new DailyExtractorConfig
                {
                    DatabaseConnectionString = LoadDatabaseConnectionFromMainConfig()
                };

                SaveConfiguration(defaultConfig);
                _logger.Info("默认配置文件创建成功");
            }
            catch (Exception ex)
            {
                _logger.Error($"创建默认配置失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 从主配置文件加载数据库连接字符串
        /// </summary>
        private string LoadDatabaseConnectionFromMainConfig()
        {
            try
            {
                var mainConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "AppConfig.xml");
                if (File.Exists(mainConfigPath))
                {
                    var xmlReader = new XmlConfigReader(mainConfigPath);
                    return xmlReader.GetConnectionString();
                }
            }
            catch (Exception ex)
            {
                _logger.Debug($"从主配置文件读取数据库连接字符串失败: {ex.Message}");
            }

            return "Data Source=192.168.20.86;Initial Catalog=DaPeng_IOServer;User Id=sa;Password=********;";
        }

        /// <summary>
        /// 获取节点值
        /// </summary>
        private string GetNodeValue(XmlNode parentNode, string nodeName, string defaultValue)
        {
            var node = parentNode?.SelectSingleNode(nodeName);
            return node?.InnerText ?? defaultValue;
        }

        /// <summary>
        /// 获取布尔值
        /// </summary>
        private bool GetBoolValue(XmlNode parentNode, string nodeName, bool defaultValue)
        {
            var value = GetNodeValue(parentNode, nodeName, defaultValue.ToString());
            return bool.TryParse(value, out bool result) ? result : defaultValue;
        }

        /// <summary>
        /// 获取整数值
        /// </summary>
        private int GetIntValue(XmlNode parentNode, string nodeName, int defaultValue)
        {
            var value = GetNodeValue(parentNode, nodeName, defaultValue.ToString());
            return int.TryParse(value, out int result) ? result : defaultValue;
        }

        /// <summary>
        /// 设置节点值
        /// </summary>
        private void SetNodeValue(XmlNode parentNode, string nodeName, string value)
        {
            var node = parentNode?.SelectSingleNode(nodeName);
            if (node != null)
            {
                node.InnerText = value;
            }
        }

        /// <summary>
        /// 获取默认配置XML
        /// </summary>
        private string GetDefaultConfigXml()
        {
            return @"<?xml version=""1.0"" encoding=""utf-8""?>
<DailyExtractorConfiguration>
  <Schedule>
    <ExecutionTime>07:00:00</ExecutionTime>
    <Enabled>true</Enabled>
  </Schedule>
  
  <ReportSettings>
    <RootPath>D:\Report\</RootPath>
    <ExcelPassword>Admin123</ExcelPassword>
    <FileNamePattern>GUSM_AutoSOSCHECK</FileNamePattern>
  </ReportSettings>
  
  <DatabaseSettings>
    <ConnectionString></ConnectionString>
    <TableName>SOSCheckResult</TableName>
    <EnableBatchInsert>true</EnableBatchInsert>
    <BatchSize>100</BatchSize>
  </DatabaseSettings>
  
  <LoggingSettings>
    <EnableDetailedLogging>true</EnableDetailedLogging>
    <LogLevel>Info</LogLevel>
    <LogFilePath>Logs\DailyExtractor.log</LogFilePath>
  </LoggingSettings>
</DailyExtractorConfiguration>";
        }
    }
}
