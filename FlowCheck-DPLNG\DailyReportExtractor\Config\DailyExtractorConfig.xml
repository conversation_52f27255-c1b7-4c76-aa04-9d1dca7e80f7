<?xml version="1.0" encoding="utf-8"?>
<DailyExtractorConfiguration>
  <!-- 定时任务调度设置 -->
  <Schedule>
    <!-- 每日执行时间 (HH:mm:ss 格式) -->
    <ExecutionTime>07:00:00</ExecutionTime>
    <!-- 是否启用自动执行 -->
    <Enabled>true</Enabled>
  </Schedule>
  
  <!-- 报告文件设置 -->
  <ReportSettings>
    <!-- 报告文件根目录路径 -->
    <RootPath>D:\Report\</RootPath>
    <!-- Excel工作表保护密码 -->
    <ExcelPassword>Admin123</ExcelPassword>
    <!-- 文件名匹配模式 -->
    <FileNamePattern>GUSM_AutoSOSCHECK</FileNamePattern>
  </ReportSettings>
  
  <!-- 数据库设置 -->
  <DatabaseSettings>
    <!-- 数据库连接字符串 (如果为空，将从主配置文件AppConfig.xml读取) -->
    <ConnectionString>Data Source=192.168.20.86;Initial Catalog=DaPeng_IOServer;User Id=sa;Password=********;</ConnectionString>
    <!-- 目标数据表名 -->
    <TableName>SOSCheckResult</TableName>
    <!-- 是否启用批量插入 -->
    <EnableBatchInsert>true</EnableBatchInsert>
    <!-- 批量插入大小 -->
    <BatchSize>100</BatchSize>
  </DatabaseSettings>
  
  <!-- 日志设置 -->
  <LoggingSettings>
    <!-- 是否启用详细日志 -->
    <EnableDetailedLogging>true</EnableDetailedLogging>
    <!-- 日志级别 (Debug, Info, Warn, Error, Fatal) -->
    <LogLevel>Info</LogLevel>
    <!-- 日志文件路径 -->
    <LogFilePath>Logs\DailyExtractor.log</LogFilePath>
  </LoggingSettings>
</DailyExtractorConfiguration>
