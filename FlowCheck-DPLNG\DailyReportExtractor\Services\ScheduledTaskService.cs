using System;
using System.Threading;
using System.Threading.Tasks;
using log4net;

namespace FlowCheck_DPLNG.DailyReportExtractor.Services
{
    /// <summary>
    /// 定时任务服务接口
    /// </summary>
    public interface IScheduledTaskService
    {
        Task StartAsync();
        Task StopAsync();
        bool IsRunning { get; }
        DateTime? NextExecutionTime { get; }
        DateTime? LastExecutionTime { get; }
        Func<Task> TaskToExecute { get; set; }
    }

    /// <summary>
    /// 定时任务服务实现
    /// 负责在指定时间自动执行任务
    /// </summary>
    public class ScheduledTaskService : IScheduledTaskService, IDisposable
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(ScheduledTaskService));

        private readonly TimeSpan _scheduledTime;
        private Timer _timer;
        private bool _isRunning;
        private bool _isExecuting;
        private DateTime? _nextExecutionTime;
        private DateTime? _lastExecutionTime;
        private readonly object _lockObject = new object();

        /// <summary>
        /// 要执行的任务委托
        /// </summary>
        public Func<Task> TaskToExecute { get; set; }

        /// <summary>
        /// 服务是否正在运行
        /// </summary>
        public bool IsRunning 
        { 
            get 
            { 
                lock (_lockObject)
                {
                    return _isRunning;
                }
            } 
        }

        /// <summary>
        /// 下次执行时间
        /// </summary>
        public DateTime? NextExecutionTime 
        { 
            get 
            { 
                lock (_lockObject)
                {
                    return _nextExecutionTime;
                }
            } 
        }

        /// <summary>
        /// 上次执行时间
        /// </summary>
        public DateTime? LastExecutionTime 
        { 
            get 
            { 
                lock (_lockObject)
                {
                    return _lastExecutionTime;
                }
            } 
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="scheduledTime">计划执行时间（每日）</param>
        public ScheduledTaskService(TimeSpan scheduledTime)
        {
            _scheduledTime = scheduledTime;
            _logger.Info($"定时任务服务初始化，计划执行时间: {scheduledTime:hh\\:mm\\:ss}");
        }

        /// <summary>
        /// 启动定时任务服务
        /// </summary>
        public async Task StartAsync()
        {
            lock (_lockObject)
            {
                if (_isRunning)
                {
                    _logger.Warn("定时任务服务已在运行中");
                    return;
                }

                _isRunning = true;
            }

            try
            {
                _logger.Info("启动定时任务服务");

                // 计算下次执行时间
                var nextExecution = CalculateNextExecutionTime();
                var delay = nextExecution - DateTime.Now;

                lock (_lockObject)
                {
                    _nextExecutionTime = nextExecution;
                }

                _logger.Info($"下次执行时间: {nextExecution:yyyy-MM-dd HH:mm:ss}, 延迟: {delay.TotalMinutes:F1} 分钟");

                // 创建定时器
                _timer = new Timer(OnTimerElapsed, null, delay, TimeSpan.FromDays(1));

                _logger.Info("定时任务服务启动成功");
            }
            catch (Exception ex)
            {
                lock (_lockObject)
                {
                    _isRunning = false;
                }
                _logger.Error($"启动定时任务服务失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 停止定时任务服务
        /// </summary>
        public async Task StopAsync()
        {
            lock (_lockObject)
            {
                if (!_isRunning)
                {
                    _logger.Warn("定时任务服务未在运行");
                    return;
                }

                _isRunning = false;
            }

            try
            {
                _logger.Info("停止定时任务服务");

                // 停止定时器
                _timer?.Dispose();
                _timer = null;

                // 等待当前任务执行完成
                var waitCount = 0;
                while (_isExecuting && waitCount < 30) // 最多等待30秒
                {
                    await Task.Delay(1000);
                    waitCount++;
                }

                if (_isExecuting)
                {
                    _logger.Warn("等待任务执行完成超时，强制停止");
                }

                lock (_lockObject)
                {
                    _nextExecutionTime = null;
                }

                _logger.Info("定时任务服务停止成功");
            }
            catch (Exception ex)
            {
                _logger.Error($"停止定时任务服务失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 手动触发任务执行
        /// </summary>
        public async Task ExecuteTaskManuallyAsync()
        {
            if (_isExecuting)
            {
                _logger.Warn("任务正在执行中，跳过手动触发");
                return;
            }

            _logger.Info("手动触发任务执行");
            await ExecuteTaskAsync();
        }

        /// <summary>
        /// 定时器回调方法
        /// </summary>
        private async void OnTimerElapsed(object state)
        {
            try
            {
                _logger.Info("定时任务触发");
                await ExecuteTaskAsync();

                // 更新下次执行时间
                lock (_lockObject)
                {
                    _nextExecutionTime = CalculateNextExecutionTime();
                }

                _logger.Info($"下次执行时间: {_nextExecutionTime:yyyy-MM-dd HH:mm:ss}");
            }
            catch (Exception ex)
            {
                _logger.Error($"定时任务执行异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 执行任务
        /// </summary>
        private async Task ExecuteTaskAsync()
        {
            if (_isExecuting)
            {
                _logger.Warn("任务已在执行中，跳过本次执行");
                return;
            }

            if (TaskToExecute == null)
            {
                _logger.Warn("未设置要执行的任务");
                return;
            }

            _isExecuting = true;
            var startTime = DateTime.Now;

            try
            {
                _logger.Info("开始执行定时任务");
                
                await TaskToExecute();

                lock (_lockObject)
                {
                    _lastExecutionTime = startTime;
                }

                var duration = DateTime.Now - startTime;
                _logger.Info($"定时任务执行完成，耗时: {duration.TotalSeconds:F2} 秒");
            }
            catch (Exception ex)
            {
                var duration = DateTime.Now - startTime;
                _logger.Error($"定时任务执行失败，耗时: {duration.TotalSeconds:F2} 秒 - {ex.Message}", ex);
            }
            finally
            {
                _isExecuting = false;
            }
        }

        /// <summary>
        /// 计算下次执行时间
        /// </summary>
        private DateTime CalculateNextExecutionTime()
        {
            var now = DateTime.Now;
            var today = now.Date;
            var scheduledToday = today.Add(_scheduledTime);

            // 如果今天的执行时间已过，则安排到明天
            if (now >= scheduledToday)
            {
                return today.AddDays(1).Add(_scheduledTime);
            }
            else
            {
                return scheduledToday;
            }
        }

        /// <summary>
        /// 获取服务状态信息
        /// </summary>
        public ServiceStatus GetStatus()
        {
            lock (_lockObject)
            {
                return new ServiceStatus
                {
                    IsRunning = _isRunning,
                    IsExecuting = _isExecuting,
                    ScheduledTime = _scheduledTime,
                    NextExecutionTime = _nextExecutionTime,
                    LastExecutionTime = _lastExecutionTime
                };
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                StopAsync().Wait(5000); // 最多等待5秒
            }
            catch (Exception ex)
            {
                _logger.Error($"释放定时任务服务资源异常: {ex.Message}", ex);
            }
        }
    }

    /// <summary>
    /// 服务状态信息
    /// </summary>
    public class ServiceStatus
    {
        public bool IsRunning { get; set; }
        public bool IsExecuting { get; set; }
        public TimeSpan ScheduledTime { get; set; }
        public DateTime? NextExecutionTime { get; set; }
        public DateTime? LastExecutionTime { get; set; }

        public override string ToString()
        {
            return $"Running: {IsRunning}, Executing: {IsExecuting}, " +
                   $"Scheduled: {ScheduledTime:hh\\:mm\\:ss}, " +
                   $"Next: {NextExecutionTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "N/A"}, " +
                   $"Last: {LastExecutionTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "N/A"}";
        }
    }
}
